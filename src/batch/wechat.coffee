###
Can only run on one server
use crontab for ever 30min
###
# TODO: more docs

exit = EXIT
puts = console.log
verbose = 2
wechat = require "../lib/wechat"
SysData = COLLECTION 'chome','sysdata'

# TODO: key read from avgs, not cfg
main = ()->
  console.log "WeChat Service Start"
  # new wechat.WeiXin SysData,'RealMaster','wxf43abc63fd6c1b19','fcd6d3800bb2906bacbee676685a0de4',(err)->
  # TODO: read config
  new wechat.WeiXin SysData,'RealMaster','wx486c08aa3a05089d','2b37e59a16594a8faebaa509a0da9956',(err)->
    if err
      return exit 1,err
    exit 0

main()